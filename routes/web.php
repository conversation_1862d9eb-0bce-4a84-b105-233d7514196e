<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Prompt Management Routes
Route::middleware(['auth', 'verified'])->prefix('prompts')->group(function () {
    Route::get('/', function () {
        return Inertia::render('prompts/Index');
    })->name('prompts.index');

    Route::get('/create', function () {
        return Inertia::render('prompts/Create');
    })->name('prompts.create');

    Route::get('/{uuid}', function ($uuid) {
        return Inertia::render('prompts/Show', ['uuid' => $uuid]);
    })->name('prompts.show');

    Route::get('/{uuid}/edit', function ($uuid) {
        return Inertia::render('prompts/Edit', ['uuid' => $uuid]);
    })->name('prompts.edit');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
