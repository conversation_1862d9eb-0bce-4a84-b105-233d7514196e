<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('responses', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('prompt_id')->constrained()->onDelete('cascade');
            $table->string('model');
            $table->longText('content');
            $table->json('metadata')->nullable();
            $table->string('status')->default('completed');     
            $table->timestamp('generated_at');
            $table->timestamps();

            $table->index(['prompt_id', 'model']);
            $table->index('uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('responses');
    }
};
