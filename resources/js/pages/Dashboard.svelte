<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { Chart, registerables } from 'chart.js';
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
    import { Badge } from '@/components/ui/badge';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import { Button } from '@/components/ui/button';
    import {
        Server,
        Activity,
        Clock,
        CheckCircle,
        XCircle,
        AlertCircle,
        Loader2,
        Database,
        Cpu,
        RefreshCw,
        ChevronLeft,
        ChevronRight
    } from 'lucide-svelte';
    import { api } from '@/lib/api';
    import type { BreadcrumbItem, DashboardData, QueueJob } from '@/types';

    // Registrar componentes do Chart.js
    Chart.register(...registerables);

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
    ];

    let dashboardData: DashboardData | null = null;
    let userQueueJobs: QueueJob[] = [];
    let loading = true;
    let error: string | null = null;
    let refreshInterval: NodeJS.Timeout;

    // Pagination state
    const ITEMS_PER_PAGE = 5;
    let ollamaModelsPage = 1;
    let runningModelsPage = 1;
    let queueJobsPage = 1;

    // Referências para os gráficos
    let promptsChart: Chart | null = null;
    let queueChart: Chart | null = null;
    let modelsChart: Chart | null = null;

    // Elementos canvas para os gráficos
    let promptsCanvas: HTMLCanvasElement;
    let queueCanvas: HTMLCanvasElement;
    let modelsCanvas: HTMLCanvasElement;

    onMount(async () => {
        await loadDashboardData();

        // Atualizar dados a cada 30 segundos
        refreshInterval = setInterval(loadDashboardData, 30000);
    });

    onDestroy(() => {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }

        // Destruir gráficos
        if (promptsChart) promptsChart.destroy();
        if (queueChart) queueChart.destroy();
        if (modelsChart) modelsChart.destroy();
    });

    async function loadDashboardData() {
        try {
            const [dashData, queueData] = await Promise.all([
                api.getDashboardData(),
                api.getUserQueueJobs()
            ]);

            dashboardData = dashData;
            userQueueJobs = queueData;
            error = null;

            // Criar/atualizar gráficos após carregar os dados
            if (dashboardData) {
                createCharts();
            }
        } catch (err) {
            console.error('Erro ao carregar dados do dashboard:', err);
            error = 'Erro ao carregar dados do dashboard';
        } finally {
            loading = false;
        }
    }

    function createCharts() {
        if (!dashboardData) return;

        // Gráfico de Prompts (Doughnut)
        createPromptsChart();

        // Gráfico de Fila (Bar)
        createQueueChart();

        // Gráfico de Modelos (Pie)
        createModelsChart();
    }

    function createPromptsChart() {
        if (!dashboardData || !promptsCanvas) return;

        if (promptsChart) {
            promptsChart.destroy();
        }

        const ctx = promptsCanvas.getContext('2d');
        if (!ctx) return;

        const stats = dashboardData.user_stats.prompts;

        promptsChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Concluídos', 'Processando', 'Pendentes', 'Falharam'],
                datasets: [{
                    data: [stats.completed, stats.processing, stats.pending, stats.failed],
                    backgroundColor: [
                        '#10b981', // green-500
                        '#3b82f6', // blue-500
                        '#f59e0b', // amber-500
                        '#ef4444'  // red-500
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0';
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    function createQueueChart() {
        if (!dashboardData || !queueCanvas) return;

        if (queueChart) {
            queueChart.destroy();
        }

        const ctx = queueCanvas.getContext('2d');
        if (!ctx) return;

        // Dados simulados de fila ao longo do tempo (últimas 24 horas)
        const hours = [];
        const queueData = [];
        const now = new Date();

        for (let i = 23; i >= 0; i--) {
            const hour = new Date(now.getTime() - (i * 60 * 60 * 1000));
            hours.push(hour.getHours() + ':00');
            // Simular dados de fila (em produção, isso viria da API)
            queueData.push(Math.floor(Math.random() * 10) + userQueueJobs.length);
        }

        queueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: hours,
                datasets: [{
                    label: 'Jobs na Fila',
                    data: queueData,
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    },
                    x: {
                        ticks: {
                            maxTicksLimit: 8
                        }
                    }
                }
            }
        });
    }

    function createModelsChart() {
        if (!dashboardData || !modelsCanvas) return;

        if (modelsChart) {
            modelsChart.destroy();
        }

        const ctx = modelsCanvas.getContext('2d');
        if (!ctx) return;

        const models = Object.keys(dashboardData.ollama.models);
        const modelUsage = models.map(() => Math.floor(Math.random() * 100) + 10); // Dados simulados

        modelsChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: models.map(model => model.split(':')[0]), // Remover tag de versão
                datasets: [{
                    label: 'Uso do Modelo (%)',
                    data: modelUsage,
                    backgroundColor: [
                        '#8b5cf6', // violet-500
                        '#06b6d4', // cyan-500
                        '#84cc16', // lime-500
                        '#f97316', // orange-500
                        '#ec4899', // pink-500
                    ],
                    borderRadius: 4,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    function formatBytes(bytes: number): string {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Pagination helper functions
    function paginateArray<T>(array: T[], page: number, itemsPerPage: number): T[] {
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return array.slice(startIndex, endIndex);
    }

    function getTotalPages(totalItems: number, itemsPerPage: number): number {
        return Math.ceil(totalItems / itemsPerPage);
    }

    // Computed values for paginated data
    $: ollamaModelsEntries = dashboardData ? Object.entries(dashboardData.ollama.models) : [];
    $: paginatedOllamaModels = paginateArray(ollamaModelsEntries, ollamaModelsPage, ITEMS_PER_PAGE);
    $: ollamaModelsTotalPages = getTotalPages(ollamaModelsEntries.length, ITEMS_PER_PAGE);

    $: paginatedRunningModels = dashboardData ? paginateArray(dashboardData.ollama.running_models, runningModelsPage, ITEMS_PER_PAGE) : [];
    $: runningModelsTotalPages = dashboardData ? getTotalPages(dashboardData.ollama.running_models.length, ITEMS_PER_PAGE) : 0;

    $: paginatedQueueJobs = paginateArray(userQueueJobs, queueJobsPage, ITEMS_PER_PAGE);
    $: queueJobsTotalPages = getTotalPages(userQueueJobs.length, ITEMS_PER_PAGE);

    async function refreshData() {
        loading = true;
        await loadDashboardData();
    }
</script>

<svelte:head>
    <title>Dashboard - QueueAI</title>
</svelte:head>

<AppLayout {breadcrumbs}>
    <div class="space-y-6 p-6">
        <!-- Header com botão de refresh -->
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold tracking-tight">Dashboard</h1>
                <p class="text-muted-foreground">
                    Monitore o status do Ollama e suas tarefas de IA
                </p>
            </div>
            <button
                on:click={refreshData}
                disabled={loading}
                class="inline-flex items-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 disabled:opacity-50"
            >
                <RefreshCw class={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Atualizar
            </button>
        </div>

        {#if error}
            <Alert variant="destructive">
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        {/if}

        {#if loading && !dashboardData}
            <div class="flex items-center justify-center py-12">
                <Loader2 class="h-8 w-8 animate-spin" />
                <span class="ml-2">Carregando dados do dashboard...</span>
            </div>
        {:else if dashboardData}
            <!-- Status Cards -->
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <!-- Ollama Status -->
                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Status Ollama</CardTitle>
                        <Server class="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div class="flex items-center space-x-2">
                            {#if dashboardData.ollama.available}
                                <Badge variant="default" class="bg-green-500">
                                    <CheckCircle class="mr-1 h-3 w-3" />
                                    Online
                                </Badge>
                            {:else}
                                <Badge variant="destructive">
                                    <XCircle class="mr-1 h-3 w-3" />
                                    Offline
                                </Badge>
                            {/if}
                        </div>
                        <p class="text-xs text-muted-foreground mt-1">
                            {dashboardData.ollama.version || 'Versão não disponível'}
                        </p>
                    </CardContent>
                </Card>

                <!-- Total Prompts -->
                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Total Prompts</CardTitle>
                        <Activity class="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold">{dashboardData.user_stats.prompts.total}</div>
                        <p class="text-xs text-muted-foreground">
                            {dashboardData.user_stats.prompts.completed} concluídos
                        </p>
                    </CardContent>
                </Card>

                <!-- Queue Jobs -->
                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Jobs na Fila</CardTitle>
                        <Clock class="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold">{dashboardData.user_stats.queue_jobs}</div>
                        <p class="text-xs text-muted-foreground">
                            Seus jobs pendentes
                        </p>
                    </CardContent>
                </Card>

                <!-- Available Models -->
                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Modelos</CardTitle>
                        <Database class="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold">{Object.keys(dashboardData.ollama.models).length}</div>
                        <p class="text-xs text-muted-foreground">
                            Modelos disponíveis
                        </p>
                    </CardContent>
                </Card>
            </div>

            <!-- Charts Section -->
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                <!-- Prompts Chart -->
                <Card>
                    <CardHeader>
                        <CardTitle>Status dos Prompts</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="h-64">
                            <canvas bind:this={promptsCanvas}></canvas>
                        </div>
                    </CardContent>
                </Card>

                <!-- Queue Chart -->
                <Card>
                    <CardHeader>
                        <CardTitle>Fila nas Últimas 24h</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="h-64">
                            <canvas bind:this={queueCanvas}></canvas>
                        </div>
                    </CardContent>
                </Card>

                <!-- Models Chart -->
                <Card>
                    <CardHeader>
                        <CardTitle>Uso dos Modelos</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="h-64">
                            <canvas bind:this={modelsCanvas}></canvas>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Detailed Information -->
            <div class="grid gap-4 md:grid-cols-2">
                <!-- Ollama Models -->
                <Card>
                    <CardHeader>
                        <CardTitle class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <Database class="h-5 w-5" />
                                Modelos Ollama
                            </div>
                            <span class="text-sm font-normal text-muted-foreground">
                                {ollamaModelsEntries.length} modelos
                            </span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-2">
                            {#each paginatedOllamaModels as [modelId, modelName]}
                                <div class="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                                    <div>
                                        <p class="font-medium text-sm">{modelName}</p>
                                        <p class="text-xs text-muted-foreground">{modelId}</p>
                                    </div>
                                </div>
                            {:else}
                                <p class="text-sm text-muted-foreground">Nenhum modelo disponível</p>
                            {/each}
                        </div>

                        <!-- Pagination for Ollama Models -->
                        {#if ollamaModelsTotalPages > 1}
                            <div class="flex items-center justify-between mt-4 pt-4 border-t">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={ollamaModelsPage <= 1}
                                    onclick={() => ollamaModelsPage = Math.max(1, ollamaModelsPage - 1)}
                                >
                                    <ChevronLeft class="h-4 w-4 mr-1" />
                                    Anterior
                                </Button>

                                <span class="text-sm text-muted-foreground">
                                    Página {ollamaModelsPage} de {ollamaModelsTotalPages}
                                </span>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={ollamaModelsPage >= ollamaModelsTotalPages}
                                    onclick={() => ollamaModelsPage = Math.min(ollamaModelsTotalPages, ollamaModelsPage + 1)}
                                >
                                    Próxima
                                    <ChevronRight class="h-4 w-4 ml-1" />
                                </Button>
                            </div>
                        {/if}
                    </CardContent>
                </Card>

                <!-- Running Models -->
                <Card>
                    <CardHeader>
                        <CardTitle class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <Cpu class="h-5 w-5" />
                                Modelos em Execução
                            </div>
                            <span class="text-sm font-normal text-muted-foreground">
                                {dashboardData.ollama.running_models.length} ativos
                            </span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-2">
                            {#each paginatedRunningModels as model}
                                <div class="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                                    <div>
                                        <p class="font-medium text-sm">{model.name}</p>
                                        <p class="text-xs text-muted-foreground">
                                            VRAM: {formatBytes(model.size_vram)}
                                        </p>
                                    </div>
                                    <Badge class="bg-green-500">
                                        <Activity class="mr-1 h-3 w-3" />
                                        Ativo
                                    </Badge>
                                </div>
                            {:else}
                                <p class="text-sm text-muted-foreground">Nenhum modelo em execução</p>
                            {/each}
                        </div>

                        <!-- Pagination for Running Models -->
                        {#if runningModelsTotalPages > 1}
                            <div class="flex items-center justify-between mt-4 pt-4 border-t">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={runningModelsPage <= 1}
                                    onclick={() => runningModelsPage = Math.max(1, runningModelsPage - 1)}
                                >
                                    <ChevronLeft class="h-4 w-4 mr-1" />
                                    Anterior
                                </Button>

                                <span class="text-sm text-muted-foreground">
                                    Página {runningModelsPage} de {runningModelsTotalPages}
                                </span>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={runningModelsPage >= runningModelsTotalPages}
                                    onclick={() => runningModelsPage = Math.min(runningModelsTotalPages, runningModelsPage + 1)}
                                >
                                    Próxima
                                    <ChevronRight class="h-4 w-4 ml-1" />
                                </Button>
                            </div>
                        {/if}
                    </CardContent>
                </Card>
            </div>

            <!-- User Queue Jobs -->
            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <Clock class="h-5 w-5" />
                        Seus Jobs na Fila ({userQueueJobs.length})
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {#if userQueueJobs.length > 0}
                        <div class="space-y-3">
                            {#each paginatedQueueJobs as job}
                                <div class="flex items-center justify-between p-3 rounded-lg border">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-2 mb-1">
                                            <Badge variant="outline">{job.model}</Badge>
                                            <span class="text-xs text-muted-foreground">
                                                #{job.id}
                                            </span>
                                        </div>
                                        <p class="text-sm text-muted-foreground mb-1">
                                            {job.prompt_content}
                                        </p>
                                        <div class="flex items-center gap-4 text-xs text-muted-foreground">
                                            <span>Criado: {new Date(typeof job.created_at === 'string' ? job.created_at : job.created_at * 1000).toLocaleString()}</span>
                                            <span>Tentativas: {job.attempts}</span>
                                            {#if job.estimated_completion}
                                                <span>Est.: {job.estimated_completion}</span>
                                            {/if}
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <Badge class="bg-blue-500">
                                            <Loader2 class="mr-1 h-3 w-3 animate-spin" />
                                            Na Fila
                                        </Badge>
                                        <button
                                            on:click={() => api.deleteQueueJob(job.id).then(refreshData)}
                                            class="text-red-500 hover:text-red-700 p-1"
                                            title="Remover da fila"
                                        >
                                            <XCircle class="h-4 w-4" />
                                        </button>
                                    </div>
                                </div>
                            {/each}
                        </div>

                        <!-- Pagination for Queue Jobs -->
                        {#if queueJobsTotalPages > 1}
                            <div class="flex items-center justify-between mt-4 pt-4 border-t">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={queueJobsPage <= 1}
                                    onclick={() => queueJobsPage = Math.max(1, queueJobsPage - 1)}
                                >
                                    <ChevronLeft class="h-4 w-4 mr-1" />
                                    Anterior
                                </Button>

                                <span class="text-sm text-muted-foreground">
                                    Página {queueJobsPage} de {queueJobsTotalPages}
                                </span>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={queueJobsPage >= queueJobsTotalPages}
                                    onclick={() => queueJobsPage = Math.min(queueJobsTotalPages, queueJobsPage + 1)}
                                >
                                    Próxima
                                    <ChevronRight class="h-4 w-4 ml-1" />
                                </Button>
                            </div>
                        {/if}
                    {:else}
                        <div class="text-center py-8">
                            <Clock class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <p class="text-muted-foreground">Nenhum job na fila</p>
                            <p class="text-sm text-muted-foreground">
                                Seus jobs aparecerão aqui quando forem adicionados à fila
                            </p>
                        </div>
                    {/if}
                </CardContent>
            </Card>

            <!-- System Information -->
            <div class="grid gap-4 md:grid-cols-3">
                <Card>
                    <CardHeader>
                        <CardTitle class="text-sm">Conexão Ollama</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p class="text-xs text-muted-foreground break-all">
                            {dashboardData.ollama.url}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle class="text-sm">Tipo de Fila</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p class="text-sm font-medium">
                            {dashboardData.system.queue.connection}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle class="text-sm">Última Atualização</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p class="text-xs text-muted-foreground">
                            {new Date(dashboardData.system.timestamp).toLocaleString()}
                        </p>
                    </CardContent>
                </Card>
            </div>
        {/if}
    </div>
</AppLayout>
