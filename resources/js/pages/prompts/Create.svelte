<script lang="ts">
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Button } from '@/components/ui/button';

    import { Textarea } from '@/components/ui/textarea';
    import { Label } from '@/components/ui/label';
    import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
    import { Checkbox } from '@/components/ui/checkbox';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import FileUpload from '@/components/prompts/FileUpload.svelte';
    import { router } from '@inertiajs/svelte';
    import { Save, AlertCircle, Loader2 } from 'lucide-svelte';
    import { api } from '@/lib/api';
    import type { CreatePromptRequest, BreadcrumbItem } from '@/types';
    import { onMount } from 'svelte';

    // State
    let loading = $state(false);
    let error = $state<string | null>(null);
    let success = $state<string | null>(null);
    let availableModels = $state<Record<string, string>>({});
    let loadingModels = $state(true);

    // Form data
    let formData: CreatePromptRequest = $state({
        content: '',
        models: [],
        attachments: [],
        metadata: {}
    });

    let selectedModels = $state<string[]>([]);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Prompts', href: '/prompts' },
        { title: 'Create', href: '/prompts/create' }
    ];

    async function loadAvailableModels() {
        try {
            loadingModels = true;
            availableModels = await api.getAvailableModelsWithNames();

            // Set default model if available
            const modelKeys = Object.keys(availableModels);
            if (modelKeys.length > 0 && selectedModels.length === 0) {
                selectedModels = [modelKeys[0]];
            }
        } catch (err) {
            console.error('Error loading models:', err);
        } finally {
            loadingModels = false;
        }
    }

    async function handleSubmit() {
        if (!formData.content.trim()) {
            error = 'Please enter prompt content';
            return;
        }

        if (selectedModels.length === 0) {
            error = 'Please select at least one model';
            return;
        }

        try {
            loading = true;
            error = null;
            success = null;

            const submitData: CreatePromptRequest = {
                ...formData,
                models: selectedModels
            };

            const result = await api.createPrompt(submitData);
            success = 'Prompt created successfully!';
            
            // Redirect to the created prompt after a short delay
            setTimeout(() => {
                router.visit(`/prompts/${result.uuid}`);
            }, 1500);
            
        } catch (err) {
            error = err instanceof Error ? err.message : 'Failed to create prompt';
            console.error('Error creating prompt:', err);
        } finally {
            loading = false;
        }
    }

    onMount(() => {
        loadAvailableModels();
    });
</script>

<svelte:head>
    <title>Create Prompt - QueueAI</title>
</svelte:head>

<AppLayout {breadcrumbs}>
    <div class="max-w-4xl mx-auto space-y-6 p-6">
        <!-- Header -->
        <div>
            <h1 class="text-2xl font-semibold tracking-tight">Create New Prompt</h1>
            <p class="text-muted-foreground">
                Create a new prompt to be processed by AI models
            </p>
        </div>

        <!-- Alerts -->
        {#if error}
            <Alert variant="destructive">
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        {/if}

        {#if success}
            <Alert>
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{success}</AlertDescription>
            </Alert>
        {/if}

        <form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="space-y-6">
            <!-- Content -->
            <Card>
                <CardHeader>
                    <CardTitle>Prompt Content</CardTitle>
                    <CardDescription>Enter the text you want to send to the AI models</CardDescription>
                </CardHeader>
                <CardContent class="space-y-4">
                    <div>
                        <Label for="content">Content *</Label>
                        <Textarea
                            id="content"
                            placeholder="Enter your prompt here..."
                            bind:value={formData.content}
                            rows={8}
                            class="resize-none"
                            required
                        />
                        <p class="text-xs text-muted-foreground mt-1">
                            {formData.content.length}/10,000 characters
                        </p>
                    </div>
                </CardContent>
            </Card>

            <!-- Models -->
            <Card>
                <CardHeader>
                    <CardTitle>AI Models</CardTitle>
                    <CardDescription>Select which models should process this prompt</CardDescription>
                </CardHeader>
                <CardContent>
                    {#if loadingModels}
                        <div class="flex items-center gap-2">
                            <Loader2 class="h-4 w-4 animate-spin" />
                            <span class="text-sm">Loading available models...</span>
                        </div>
                    {:else if Object.keys(availableModels).length === 0}
                        <div class="text-center py-4">
                            <p class="text-sm text-muted-foreground">No models available</p>
                            <p class="text-xs text-muted-foreground mt-1">Please check your AI service configuration</p>
                        </div>
                    {:else}
                        <div class="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                            {#each Object.entries(availableModels) as [modelId, modelName]}
                                <div class="flex items-center space-x-2">
                                    <Checkbox
                                        id={modelId}
                                        checked={selectedModels.includes(modelId)}
                                        onCheckedChange={(checked) => {
                                            if (checked) {
                                                selectedModels = [...selectedModels, modelId];
                                            } else {
                                                selectedModels = selectedModels.filter(m => m !== modelId);
                                            }
                                        }}
                                    />
                                    <Label for={modelId} class="text-sm font-normal">
                                        {modelName}
                                    </Label>
                                </div>
                            {/each}
                        </div>
                    {/if}
                </CardContent>
            </Card>

            <!-- File Attachments -->
            <Card>
                <CardHeader>
                    <CardTitle>File Attachments</CardTitle>
                    <CardDescription>
                        Upload files to include with your prompt (max 5 files, 10MB each)
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <FileUpload
                        files={formData.attachments || []}
                        onFilesChange={(files) => formData.attachments = files}
                        disabled={loading}
                    />
                </CardContent>
            </Card>

            <!-- Actions -->
            <div class="flex items-center gap-4">
                <Button type="submit" disabled={loading}>
                    {#if loading}
                        <Loader2 class="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                    {:else}
                        <Save class="mr-2 h-4 w-4" />
                        Create Prompt
                    {/if}
                </Button>
                
                <Button type="button" variant="outline" onclick={() => router.visit('/prompts')}>
                    Cancel
                </Button>
            </div>
        </form>
    </div>
</AppLayout>
