<script lang="ts">
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Button } from '@/components/ui/button';
    import { Textarea } from '@/components/ui/textarea';
    import { Label } from '@/components/ui/label';
    import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import StatusBadge from '@/components/prompts/StatusBadge.svelte';
    import { router } from '@inertiajs/svelte';
    import { Save, AlertCircle, Loader2, ArrowLeft } from 'lucide-svelte';
    import { api } from '@/lib/api';
    import type { Prompt, BreadcrumbItem } from '@/types';
    import { onMount } from 'svelte';

    interface Props {
        uuid: string;
    }

    let { uuid }: Props = $props();

    // State
    let prompt = $state<Prompt | null>(null);
    let loading = $state(true);
    let saving = $state(false);
    let error = $state<string | null>(null);
    let success = $state<string | null>(null);

    // Form data
    let formData = $state({
        content: '',
        metadata: []
    });

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Prompts', href: '/prompts' },
        { title: `Prompt #${uuid.substring(0, 8)}`, href: `/prompts/${uuid}` },
        { title: 'Edit', href: `/prompts/${uuid}/edit` }
    ];

    async function loadPrompt() {
        try {
            loading = true;
            error = null;
            prompt = await api.getPrompt(uuid);
            
            // Initialize form data
            formData.content = prompt.content;
            formData.metadata = prompt.metadata || [];
        } catch (err) {
            error = err instanceof Error ? err.message : 'Failed to load prompt';
            console.error('Error loading prompt:', err);
        } finally {
            loading = false;
        }
    }

    async function handleSubmit() {
        if (!formData.content.trim()) {
            error = 'Please enter prompt content';
            return;
        }

        try {
            saving = true;
            error = null;
            success = null;

            await api.updatePrompt(uuid, formData);
            success = 'Prompt updated successfully!';
            
            // Redirect back to the prompt after a short delay
            setTimeout(() => {
                router.visit(`/prompts/${uuid}`);
            }, 1500);
            
        } catch (err) {
            error = err instanceof Error ? err.message : 'Failed to update prompt';
            console.error('Error updating prompt:', err);
        } finally {
            saving = false;
        }
    }

    function formatDate(dateString: string): string {
        return new Date(dateString).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    onMount(() => {
        loadPrompt();
    });
</script>

<svelte:head>
    <title>Edit Prompt #{uuid.substring(0, 8)} - QueueAI</title>
</svelte:head>

<AppLayout {breadcrumbs}>
    <div class="max-w-4xl mx-auto space-y-6 p-6">
        {#if loading}
            <div class="flex items-center justify-center py-12">
                <Loader2 class="h-6 w-6 animate-spin mr-2" />
                <span>Loading prompt...</span>
            </div>
        {:else if error && !prompt}
            <Alert variant="destructive">
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
            </Alert>
            <div class="flex justify-center">
                <Button variant="outline" onclick={loadPrompt}>
                    <Loader2 class="mr-2 h-4 w-4" />
                    Try Again
                </Button>
            </div>
        {:else if prompt}
            <!-- Header -->
            <div class="flex items-start justify-between">
                <div>
                    <h1 class="text-2xl font-semibold tracking-tight">
                        Edit Prompt #{prompt.uuid.substring(0, 8)}
                    </h1>
                    <div class="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                        <div class="flex items-center gap-2">
                            <StatusBadge status={prompt.status} />
                        </div>
                        <span>Created {formatDate(prompt.created_at)}</span>
                    </div>
                </div>
                <Button variant="outline" href={`/prompts/${prompt.uuid}`}>
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    Back to Prompt
                </Button>
            </div>

            <!-- Alerts -->
            {#if error}
                <Alert variant="destructive">
                    <AlertCircle class="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            {/if}

            {#if success}
                <Alert>
                    <AlertCircle class="h-4 w-4" />
                    <AlertDescription>{success}</AlertDescription>
                </Alert>
            {/if}

            <!-- Edit restrictions notice -->
            {#if prompt.status !== 'pending'}
                <Alert>
                    <AlertCircle class="h-4 w-4" />
                    <AlertDescription>
                        This prompt cannot be edited because it has already been processed. 
                        Only pending prompts can be modified.
                    </AlertDescription>
                </Alert>
            {:else}
                <form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="space-y-6">
                    <!-- Content -->
                    <Card>
                        <CardHeader>
                            <CardTitle>Prompt Content</CardTitle>
                            <CardDescription>Edit the text you want to send to the AI models</CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div>
                                <Label for="content">Content *</Label>
                                <Textarea
                                    id="content"
                                    placeholder="Enter your prompt here..."
                                    bind:value={formData.content}
                                    rows={8}
                                    class="resize-none"
                                    required
                                />
                                <p class="text-xs text-muted-foreground mt-1">
                                    {formData.content.length}/10,000 characters
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- Current Attachments (read-only) -->
                    {#if prompt.attachments && prompt.attachments.length > 0}
                        <Card>
                            <CardHeader>
                                <CardTitle>Current Attachments</CardTitle>
                                <CardDescription>
                                    Attachments cannot be modified after creation
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div class="space-y-2">
                                    {#each prompt.attachments as attachment}
                                        <div class="flex items-center justify-between p-2 border rounded bg-muted/50">
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium truncate">{attachment.name}</p>
                                                <p class="text-xs text-muted-foreground">
                                                    {(attachment.size / 1024).toFixed(1)} KB
                                                </p>
                                            </div>
                                        </div>
                                    {/each}
                                </div>
                            </CardContent>
                        </Card>
                    {/if}

                    <!-- Actions -->
                    <div class="flex items-center gap-4">
                        <Button type="submit" disabled={saving}>
                            {#if saving}
                                <Loader2 class="mr-2 h-4 w-4 animate-spin" />
                                Saving...
                            {:else}
                                <Save class="mr-2 h-4 w-4" />
                                Save Changes
                            {/if}
                        </Button>
                        
                        <Button type="button" variant="outline" onclick={() => router.visit(`/prompts/${uuid}`)}>
                            Cancel
                        </Button>
                    </div>
                </form>
            {/if}
        {/if}
    </div>
</AppLayout>
