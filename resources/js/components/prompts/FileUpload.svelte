<script lang="ts">
    import { Button } from '@/components/ui/button';
    import { Upload, X } from 'lucide-svelte';

    interface Props {
        files?: File[];
        onFilesChange?: (files: File[]) => void;
        maxFiles?: number;
        maxFileSize?: number; // in MB
        acceptedTypes?: string;
        disabled?: boolean;
        class?: string;
    }

    let {
        files = [],
        onFilesChange,
        maxFiles = 5,
        maxFileSize = 10,
        acceptedTypes = "image/*,.pdf,.txt,.doc,.docx",
        disabled = false,
        class: className = ''
    }: Props = $props();

    let fileInput: HTMLInputElement;

    function handleFileSelect(event: Event) {
        const target = event.target as HTMLInputElement;
        if (target.files) {
            const newFiles = Array.from(target.files);
            const validFiles = newFiles.filter(file => {
                // Check file size
                if (file.size > maxFileSize * 1024 * 1024) {
                    alert(`File ${file.name} is too large. Maximum size is ${maxFileSize}MB.`);
                    return false;
                }
                return true;
            });

            const updatedFiles = [...files, ...validFiles].slice(0, maxFiles);
            onFilesChange?.(updatedFiles);
        }
        
        // Reset input value to allow selecting the same file again
        target.value = '';
    }

    function removeFile(index: number) {
        const updatedFiles = files.filter((_, i) => i !== index);
        onFilesChange?.(updatedFiles);
    }

    function formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
</script>

<div class={`space-y-4 ${className}`}>
    <div>
        <input
            type="file"
            bind:this={fileInput}
            onchange={handleFileSelect}
            multiple
            accept={acceptedTypes}
            class="hidden"
        />
        <Button
            type="button"
            variant="outline"
            onclick={() => fileInput?.click()}
            {disabled}
        >
            <Upload class="mr-2 h-4 w-4" />
            Choose Files
        </Button>
        <p class="text-xs text-muted-foreground mt-1">
            Max {maxFiles} files, {maxFileSize}MB each. Supported: {acceptedTypes}
        </p>
    </div>

    {#if files.length > 0}
        <div class="space-y-2">
            {#each files as file, index}
                <div class="flex items-center justify-between p-2 border rounded">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium truncate">{file.name}</p>
                        <p class="text-xs text-muted-foreground">
                            {formatFileSize(file.size)}
                        </p>
                    </div>
                    <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onclick={() => removeFile(index)}
                        {disabled}
                    >
                        <X class="h-4 w-4" />
                    </Button>
                </div>
            {/each}
        </div>
    {/if}
</div>
