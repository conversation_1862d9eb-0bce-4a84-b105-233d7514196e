<script lang="ts">
    import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
    import { Button } from '@/components/ui/button';
    import { Badge } from '@/components/ui/badge';
    import StatusBadge from './StatusBadge.svelte';
    import { Eye, Edit, Paperclip, MessageSquare } from 'lucide-svelte';
    import type { Prompt } from '@/types';

    interface Props {
        prompt: Prompt;
        class?: string;
    }

    let { prompt, class: className = '' }: Props = $props();

    function formatDate(dateString: string): string {
        return new Date(dateString).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function truncateContent(content: string, maxLength = 150): string {
        if (content.length <= maxLength) return content;
        return content.substring(0, maxLength) + '...';
    }
</script>

<Card class={`hover:shadow-md transition-shadow ${className}`}>
    <CardHeader class="pb-3">
        <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
                <CardTitle class="text-base font-medium truncate">
                    Prompt #{prompt.uuid.substring(0, 8)}
                </CardTitle>
                <CardDescription class="text-sm text-muted-foreground">
                    Created {formatDate(prompt.created_at)}
                </CardDescription>
            </div>
            <StatusBadge status={prompt.status} />
        </div>
    </CardHeader>
    
    <CardContent class="pt-0">
        <div class="space-y-3">
            <!-- Content Preview -->
            <p class="text-sm text-foreground leading-relaxed">
                {truncateContent(prompt.content)}
            </p>
            
            <!-- Metadata -->
            <div class="flex items-center gap-4 text-xs text-muted-foreground">
                {#if prompt.responses && prompt.responses.length > 0}
                    <div class="flex items-center gap-1">
                        <MessageSquare class="h-3 w-3" />
                        <span>{prompt.responses.length} response{prompt.responses.length !== 1 ? 's' : ''}</span>
                    </div>
                {/if}
                
                {#if prompt.attachments && prompt.attachments.length > 0}
                    <div class="flex items-center gap-1">
                        <Paperclip class="h-3 w-3" />
                        <span>{prompt.attachments.length} file{prompt.attachments.length !== 1 ? 's' : ''}</span>
                    </div>
                {/if}
                
                {#if prompt.processed_at}
                    <span>Processed {formatDate(prompt.processed_at)}</span>
                {/if}
            </div>
            
            <!-- Models used -->
            {#if prompt.responses && prompt.responses.length > 0}
                <div class="flex flex-wrap gap-1">
                    {#each [...new Set(prompt.responses.map(r => r.model))] as model}
                        <Badge variant="outline" class="text-xs">
                            {model}
                        </Badge>
                    {/each}
                </div>
            {/if}
            
            <!-- Actions -->
            <div class="flex items-center gap-2 pt-2">
                <Button variant="outline" size="sm" href={`/prompts/${prompt.uuid}`}>
                    <Eye class="h-3 w-3 mr-1" />
                    View
                </Button>

                <Button variant="outline" size="sm" href={`/prompts/${prompt.uuid}/edit`}>
                    <Edit class="h-3 w-3 mr-1" />
                    Edit
                </Button>
            </div>
        </div>
    </CardContent>
</Card>
