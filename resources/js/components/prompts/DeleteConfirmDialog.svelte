<script lang="ts">
    import { Button } from '@/components/ui/button';
    import { 
        AlertDialog, 
        AlertDialogAction, 
        AlertDialogCancel, 
        AlertDialogContent, 
        AlertDialogDescription, 
        AlertDialogFooter, 
        AlertDialogHeader, 
        AlertDialogTitle, 
        AlertDialogTrigger 
    } from '@/components/ui/alert-dialog';
    import { Trash2, Loader2 } from 'lucide-svelte';

    interface Props {
        title: string;
        description: string;
        onConfirm: () => Promise<void>;
        loading?: boolean;
        variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
        size?: 'default' | 'sm' | 'lg' | 'icon';
        triggerText?: string;
        showIcon?: boolean;
    }

    let { 
        title, 
        description, 
        onConfirm, 
        loading = false,
        variant = 'destructive',
        size = 'sm',
        triggerText = 'Delete',
        showIcon = true
    }: Props = $props();

    let open = $state(false);
    let isDeleting = $state(false);

    const handleConfirm = async () => {
        try {
            isDeleting = true;
            await onConfirm();
            open = false;
        } catch (error) {
            console.error('Delete error:', error);
        } finally {
            isDeleting = false;
        }
    };
</script>

<AlertDialog bind:open>
    <AlertDialogTrigger>
        <Button
            {variant}
            {size}
            disabled={loading}
        >
            {#if loading}
                <Loader2 class="h-4 w-4 animate-spin" />
            {:else if showIcon}
                <Trash2 class="h-4 w-4" />
            {/if}
            {#if size !== 'icon'}
                <span class={showIcon ? 'ml-1' : ''}>{triggerText}</span>
            {/if}
        </Button>
    </AlertDialogTrigger>
    <AlertDialogContent>
        <AlertDialogHeader>
            <AlertDialogTitle>{title}</AlertDialogTitle>
            <AlertDialogDescription>
                {description}
            </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
                onclick={handleConfirm}
                disabled={isDeleting}
                class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
                {#if isDeleting}
                    <Loader2 class="h-4 w-4 animate-spin mr-2" />
                    Deletando...
                {:else}
                    <Trash2 class="h-4 w-4 mr-2" />
                    Confirmar
                {/if}
            </AlertDialogAction>
        </AlertDialogFooter>
    </AlertDialogContent>
</AlertDialog>
