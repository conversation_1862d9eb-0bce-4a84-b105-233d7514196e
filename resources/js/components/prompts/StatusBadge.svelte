<script lang="ts">
    import { Badge } from '@/components/ui/badge';
    import { Clock, CheckCircle, XCircle, Loader2 } from 'lucide-svelte';

    interface Props {
        status: 'pending' | 'processing' | 'completed' | 'failed';
        class?: string;
    }

    let { status, class: className = '' }: Props = $props();

    const statusConfig = {
        pending: {
            variant: 'secondary' as const,
            icon: Clock,
            label: 'Pending',
            class: 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-950 dark:border-yellow-800'
        },
        processing: {
            variant: 'secondary' as const,
            icon: Loader2,
            label: 'Processing',
            class: 'text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-950 dark:border-blue-800'
        },
        completed: {
            variant: 'secondary' as const,
            icon: CheckCircle,
            label: 'Completed',
            class: 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-950 dark:border-green-800'
        },
        failed: {
            variant: 'secondary' as const,
            icon: XCircle,
            label: 'Failed',
            class: 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-950 dark:border-red-800'
        }
    };

    const config = statusConfig[status];
</script>

<Badge variant={config.variant} class={`${config.class} ${className}`}>
    {@const IconComponent = config.icon}
    <IconComponent class="mr-1 h-3 w-3 {status === 'processing' ? 'animate-spin' : ''}" />
    {config.label}
</Badge>
