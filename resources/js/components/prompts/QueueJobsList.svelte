<script lang="ts">
    import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
    import { Badge } from '@/components/ui/badge';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import {
        Clock,
        Loader2,
        AlertCircle,
        RefreshCw,
        Server,
        Trash2
    } from 'lucide-svelte';
    import DeleteConfirmDialog from './DeleteConfirmDialog.svelte';
    import AddJobDialog from './AddJobDialog.svelte';
    import { api } from '@/lib/api';
    import type { QueueJob } from '@/types';
    import { onMount, onDestroy } from 'svelte';

    interface Props {
        showAddButton?: boolean;
        promptUuid?: string;
        existingModels?: string[];
        onJobCreated?: () => void;
    }

    let {
        showAddButton = false,
        promptUuid,
        existingModels = [],
        onJobCreated
    }: Props = $props();

    let jobs: QueueJob[] = $state([]);
    let loading = $state(true);
    let error: string | null = $state(null);
    let refreshInterval: NodeJS.Timeout | null = null;

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const loadJobs = async () => {
        try {
            error = null;
            jobs = await api.getQueueJobs();
        } catch (err) {
            console.error('Error loading queue jobs:', err);
            error = 'Erro ao carregar jobs da fila';
        } finally {
            loading = false;
        }
    };

    const refreshJobs = async () => {
        try {
            jobs = await api.getQueueJobs();
        } catch (err) {
            console.error('Error refreshing queue jobs:', err);
        }
    };

    const deleteJob = async (jobId: number) => {
        try {
            await api.deleteQueueJob(jobId);
            // Atualizar a lista após deletar
            await refreshJobs();
        } catch (err) {
            console.error('Error deleting job:', err);
        }
    };

    onMount(() => {
        loadJobs();
        
        // Auto-refresh a cada 5 segundos
        refreshInterval = setInterval(refreshJobs, 5000);
    });

    onDestroy(() => {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    });
</script>

<Card>
    <CardHeader>
        <div class="flex items-center justify-between">
            <CardTitle class="flex items-center gap-2">
                <Server class="h-5 w-5" />
                Jobs na Fila
                {#if jobs.length > 0}
                    <Badge variant="secondary">{jobs.length}</Badge>
                {/if}
            </CardTitle>
            <div class="flex items-center gap-2">
                {#if showAddButton && promptUuid}
                    <AddJobDialog
                        {promptUuid}
                        {existingModels}
                        onJobCreated={() => {
                            refreshJobs();
                            if (onJobCreated) onJobCreated();
                        }}
                    />
                {/if}
                <button
                    class="p-1 hover:bg-gray-100 rounded-md transition-colors"
                    onclick={refreshJobs}
                    title="Atualizar"
                >
                    <RefreshCw class="h-4 w-4" />
                </button>
            </div>
        </div>
    </CardHeader>
    <CardContent>
        {#if loading}
            <div class="flex items-center justify-center py-8">
                <Loader2 class="h-6 w-6 animate-spin mr-2" />
                <span>Carregando jobs...</span>
            </div>
        {:else if error}
            <Alert variant="destructive">
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        {:else if jobs.length === 0}
            <div class="text-center py-8 text-muted-foreground">
                <Server class="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>Nenhum job na fila</p>
                <p class="text-sm">Todos os prompts foram processados</p>
            </div>
        {:else}
            <div class="space-y-3">
                {#each jobs as job}
                    <div class="border rounded-lg p-3 bg-gray-50/50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="flex items-center gap-2">
                                    <Clock class="h-4 w-4 text-muted-foreground" />
                                    <span class="text-sm font-medium">
                                        #{job.prompt_uuid?.substring(0, 8) || 'N/A'}
                                    </span>
                                </div>
                                {#if job.model}
                                    <Badge variant="outline" class="text-xs">
                                        {job.model}
                                    </Badge>
                                {/if}
                                {#if job.attempts > 0}
                                    <Badge variant="secondary" class="text-xs">
                                        Tentativa {job.attempts + 1}
                                    </Badge>
                                {/if}
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="text-xs text-muted-foreground">
                                    {formatDate(job.created_at)}
                                </div>
                                <DeleteConfirmDialog
                                    title="Remover Job"
                                    description="Tem certeza que deseja remover este job da fila? O processamento será cancelado."
                                    onConfirm={() => deleteJob(job.id)}
                                    variant="ghost"
                                    size="icon"
                                    showIcon={true}
                                />
                            </div>
                        </div>
                    </div>
                {/each}
            </div>
        {/if}
    </CardContent>
</Card>
