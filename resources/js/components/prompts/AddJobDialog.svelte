<script lang="ts">
    import { <PERSON><PERSON> } from '@/components/ui/button';
    import { 
        <PERSON>alog, 
        DialogContent, 
        DialogDescription, 
        DialogFooter, 
        DialogHeader, 
        DialogTitle, 
        DialogTrigger 
    } from '@/components/ui/dialog';
    import { Badge } from '@/components/ui/badge';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import { 
        Plus, 
        Loader2, 
        AlertCircle,
        Server,
        CheckCircle
    } from 'lucide-svelte';
    import { api } from '@/lib/api';

    interface Props {
        promptUuid: string;
        existingModels?: string[];
        onJobCreated?: () => void;
    }

    let { 
        promptUuid, 
        existingModels = [],
        onJobCreated
    }: Props = $props();

    let open = $state(false);
    let loading = $state(false);
    let creating = $state(false);
    let error: string | null = $state(null);
    let availableModels: string[] = $state([]);
    let selectedModel: string | null = $state(null);

    // Filtrar modelos que já têm respostas
    const availableNewModels = $derived(
        Array.isArray(availableModels) ? availableModels.filter(model => !existingModels.includes(model)) : []
    );

    const loadModels = async () => {
        try {
            loading = true;
            error = null;
            availableModels = await api.getSystemModels();
        } catch (err) {
            console.error('Error loading models:', err);
            error = 'Erro ao carregar modelos disponíveis';
        } finally {
            loading = false;
        }
    };

    const createJob = async () => {
        if (!selectedModel) return;
        
        try {
            creating = true;
            error = null;
            
            await api.createJob(promptUuid, selectedModel);
            
            // Fechar dialog e resetar estado
            open = false;
            selectedModel = null;
            
            // Callback para atualizar a lista
            if (onJobCreated) {
                onJobCreated();
            }
        } catch (err) {
            console.error('Error creating job:', err);
            error = 'Erro ao criar job. Verifique se já não existe uma resposta para este modelo.';
        } finally {
            creating = false;
        }
    };

    // Carregar modelos quando o dialog abrir
    $effect(() => {
        if (open && availableModels.length === 0) {
            loadModels();
        }
    });
</script>

<Dialog bind:open>
    <DialogTrigger>
        <Button variant="outline" size="sm">
            <Plus class="h-4 w-4 mr-2" />
            Add Job
        </Button>
    </DialogTrigger>
    <DialogContent class="sm:max-w-md">
        <DialogHeader>
            <DialogTitle class="flex items-center gap-2">
                <Server class="h-5 w-5" />
                Adicionar Job à Fila
            </DialogTitle>
            <DialogDescription>
                Selecione um modelo para processar este prompt.
            </DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
            {#if loading}
                <div class="flex items-center justify-center py-8">
                    <Loader2 class="h-6 w-6 animate-spin mr-2" />
                    <span>Carregando modelos...</span>
                </div>
            {:else if error}
                <Alert variant="destructive">
                    <AlertCircle class="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            {:else if availableNewModels.length === 0}
                <Alert>
                    <CheckCircle class="h-4 w-4" />
                    <AlertDescription>
                        Todos os modelos disponíveis já foram processados para este prompt.
                    </AlertDescription>
                </Alert>
            {:else}
                <div class="space-y-2">
                    <div class="text-sm font-medium">Modelos Disponíveis:</div>
                    <div class="grid gap-2 max-h-60 overflow-y-auto">
                        {#each availableNewModels as model}
                            <button
                                class="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors {selectedModel === model ? 'border-blue-500 bg-blue-50' : ''}"
                                onclick={() => selectedModel = model}
                            >
                                <span class="font-medium">{model}</span>
                                {#if selectedModel === model}
                                    <CheckCircle class="h-4 w-4 text-blue-500" />
                                {/if}
                            </button>
                        {/each}
                    </div>
                </div>

                {#if existingModels.length > 0}
                    <div class="space-y-2">
                        <div class="text-sm font-medium text-muted-foreground">Já Processados:</div>
                        <div class="flex flex-wrap gap-1">
                            {#each existingModels as model}
                                <Badge variant="secondary" class="text-xs">
                                    {model}
                                </Badge>
                            {/each}
                        </div>
                    </div>
                {/if}
            {/if}
        </div>

        <DialogFooter>
            <Button 
                variant="outline" 
                onclick={() => open = false}
                disabled={creating}
            >
                Cancelar
            </Button>
            <Button 
                onclick={createJob}
                disabled={!selectedModel || creating || availableNewModels.length === 0}
            >
                {#if creating}
                    <Loader2 class="h-4 w-4 animate-spin mr-2" />
                    Criando...
                {:else}
                    <Plus class="h-4 w-4 mr-2" />
                    Criar Job
                {/if}
            </Button>
        </DialogFooter>
    </DialogContent>
</Dialog>
