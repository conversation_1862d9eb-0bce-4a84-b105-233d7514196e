<script lang="ts">
    import { Badge } from '@/components/ui/badge';
    import { Clock } from 'lucide-svelte';
    import { formatProcessingTime } from '@/lib/utils/time';
    import type { Response } from '@/types';

    interface Props {
        response: Response;
    }

    let { response }: Props = $props();

    const processingTime = $derived(response.metadata?.processing_time);
    const hasProcessingTime = $derived(processingTime && typeof processingTime === 'number');
</script>

{#if hasProcessingTime && processingTime}
    <Badge variant="outline" class="text-xs flex items-center gap-1">
        <Clock class="h-3 w-3" />
        {formatProcessingTime(processingTime)}
    </Badge>
{/if}
