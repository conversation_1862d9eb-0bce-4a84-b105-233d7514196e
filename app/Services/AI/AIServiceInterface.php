<?php

namespace App\Services\AI;

interface AIServiceInterface
{
    /**
     * Envia uma mensagem para o modelo de IA e retorna a resposta.
     *
     * @param string $prompt O prompt ou mensagem a ser enviada
     * @param array $options Opções adicionais para a requisição
     * @return string A resposta do modelo de IA
     */
    public function sendMessage(string $prompt, array $options = []): string;

    /**
     * Envia uma conversa completa para o modelo de IA e retorna a resposta.
     *
     * @param array $messages Array de mensagens no formato [['role' => 'user|assistant|system', 'content' => 'mensagem']]
     * @param array $options Opções adicionais para a requisição
     * @return string A resposta do modelo de IA
     */
    public function sendConversation(array $messages, array $options = []): string;

    /**
     * Gera embeddings para um texto.
     *
     * @param string $text O texto para gerar embeddings
     * @return array O vetor de embeddings
     */
    public function generateEmbeddings(string $text): array;

    /**
     * Retorna o nome do serviço de IA.
     *
     * @return string
     */
    public function getServiceName(): string;

    /**
     * Verifica se o serviço está configurado corretamente.
     *
     * @return bool
     */
    public function isConfigured(): bool;

    // AIServiceInterface.php
    public function sendMessageWithAttachments(string $prompt, array $options = [], array $attachments = []): string;
}
