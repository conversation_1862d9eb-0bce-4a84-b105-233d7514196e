<?php

namespace App\Services\AI;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

use Illuminate\Http\UploadedFile;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class OllamaService extends BaseAIService
{
    /**
     * URL base da API.
     *
     * @var string
     */
    protected string $apiUrl;

    /**
     * Construtor.
     *
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        $this->serviceName = 'Ollama';
        $this->apiUrl = $config['api_url'] ?? env('OLLAMA_API_URL', 'http://localhost:11434/api');
        $this->apiUrl = trim($this->apiUrl, '/');
        $this->defaultModel = $config['model'] ?? env('OLLAMA_DEFAULT_MODEL');

        $this->defaultOptions = [
            'model' => $this->defaultModel,
            'temperature' => 0.7,
            'num_predict' => 1000,
            'top_k' => 40,
            'top_p' => 0.9,
            'stream' => false,
        ];

        parent::__construct($config);
    }

    /**
     * {@inheritdoc}
     */
    public function isConfigured(): bool
    {
        // Ollama não precisa de API key, apenas verifica se a URL está configurada
        return !empty($this->apiUrl);
    }

    /**
     * Obter a URL da API
     */
    public function getApiUrl(): string
    {
        return $this->apiUrl;
    }

    /**
     * Testa a conexão com o servidor Ollama.
     *
     * @return bool
     */
    public function testConnection(): bool
    {
        if (!$this->isConfigured()) {
            return false;
        }

        // Tentar fazer uma requisição simples para o endpoint de lista de modelos
        Log::info('Testando conexão com Ollama', ['url' => $this->apiUrl]);

        // Tentar vários endpoints possíveis
        $endpoint = "{$this->apiUrl}/tags";

        $response = Http::timeout(5)->get($endpoint);

        if ($response->successful()) {
            Log::info('Conexão com Ollama bem-sucedida', [
                'endpoint' => $endpoint,
                'status' => $response->status(),
                'body' => $response->json()
            ]);
            return true;
        } else {
            Log::warning('Falha ao conectar com Ollama', [
                'endpoint' => $endpoint,
                'status' => $response->status(),
                'body' => $response->body()
            ]);
        }


        return false;
    }

    /**
     * {@inheritdoc}
     */
    public function sendMessage(string $prompt, array $options = []): string
    {
        $messages = [
            ['role' => 'user', 'content' => $prompt]
        ];

        return $this->sendConversation($messages, $options);
    }

    /**
     * Envia uma mensagem com possíveis anexos de imagem
     *
     * @param string $prompt
     * @param array $options
     * @param array $attachments Array de Media objects ou caminhos de arquivos
     * @return string
     */
    public function sendMessageWithAttachments(string $prompt, array $options = [], array $attachments = []): string
    {
        $messages = [
            [
                'role' => 'user',
                'content' => $prompt,
                'images' => $this->processAttachments($attachments)
            ]
        ];
        return $this->sendConversation($messages, $options);
    }

    /**
     * Processa anexos para o formato esperado pelo Ollama
     *
     * @param array $attachments
     * @return array
     */
    protected function processAttachments(array $attachments): array
    {
        return collect($attachments)->map(function ($attachment) {
            if ($attachment instanceof Media) {
                // Se for um objeto Media do Spatie
                if (str_starts_with($attachment->mime_type, 'image/')) {
                    return base64_encode(file_get_contents($attachment->getPath()));
                }
            } elseif (is_string($attachment) && file_exists($attachment)) {
                // Se for um caminho de arquivo
                $mime = mime_content_type($attachment);
                if (str_starts_with($mime, 'image/')) {
                    return base64_encode(file_get_contents($attachment));
                }
            } elseif ($attachment instanceof UploadedFile) {
                // Se for um arquivo enviado
                if (str_starts_with($attachment->getMimeType(), 'image/')) {
                    return base64_encode(file_get_contents($attachment->getRealPath()));
                }
            }
        })->filter()->values()->all();
    }

    /**
     * {@inheritdoc}
     * Modificado para suportar imagens nas mensagens
     */
    public function sendConversation(array $messages, array $options = []): string
    {
        if (!$this->isConfigured()) {
            throw new Exception($this->formatErrorMessage('API URL not configured'));
        }

        $options = $this->mergeOptions($options);
        $ollamaMessages = $this->convertToOllamaFormat($messages);

        $payload = [
            'model' => $options['model'],
            'messages' => $ollamaMessages,
            'options' => [
                'temperature' => $options['temperature'],
                'num_predict' => $options['num_predict'],
                'top_k' => $options['top_k'],
                'top_p' => $options['top_p'],
            ],
            'stream' => $options['stream'],
            'keep_alive' => 0,
        ];
        $timeout = $this->defaultOptions['timeout'] ?? 120;

        $response = Http::timeout($timeout)
            ->withHeaders(['Content-Type' => 'application/json'])
            ->post("{$this->apiUrl}/chat", $payload);

        if ($response->successful()) {
            $data = $response->json();
            return $data['message']['content'] ?? '';
        } else {
            $errorMessage = "API request failed with status {$response->status()}: {$response->body()}";
            $this->logError('API request failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'url' => "{$this->apiUrl}/chat",
                'model' => $options['model']
            ]);
            throw new Exception($this->formatErrorMessage($errorMessage));
        }
    }

    /**
     * {@inheritdoc}
     */
    public function generateEmbeddings(string $text): array
    {
        if (!$this->isConfigured()) {
            $this->logError('API URL not configured');
            return [];
        }

        $response = Http::post("{$this->apiUrl}/embeddings", [
            'model' => $this->defaultModel,
            'prompt' => $text,
        ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['embedding'] ?? [];
        } else {
            $this->logError('Embeddings API request failed', $response->body());
            return [];
        }
    }

    /**
     * Converte mensagens do formato OpenAI para o formato Ollama.
     *
     * @param array $messages
     * @return array
     */
    protected function convertToOllamaFormat(array $messages): array
    {
        $ollamaMessages = [];

        foreach ($messages as $message) {
            $role = $message['role'] ?? 'user';
            $content = $message['content'] ?? '';

            // Criar a mensagem base
            $ollamaMessage = [
                'role' => $role,
                'content' => $content
            ];

            // Preservar campos adicionais suportados pelo Ollama (como images)
            $additionalFields = ['images', 'options']; // Adicione outros campos suportados aqui
            foreach ($additionalFields as $field) {
                if (isset($message[$field])) {
                    $ollamaMessage[$field] = $message[$field];
                }
            }

            $ollamaMessages[] = $ollamaMessage;
        }

        return $ollamaMessages;
    }

    /**
     * Envia uma requisição para o endpoint de completions (para modelos que não suportam chat).
     *
     * @param string $prompt
     * @param array $options
     * @return string
     */
    public function generateCompletion(string $prompt, array $options = []): string
    {
        if (!$this->isConfigured()) {
            throw new Exception($this->formatErrorMessage('API URL not configured'));
        }

        $options = $this->mergeOptions($options);

        $response = Http::post("{$this->apiUrl}/generate", [
            'model' => $options['model'],
            'prompt' => $prompt,
            'options' => [
                'temperature' => $options['temperature'],
                'num_predict' => $options['num_predict'],
                'top_k' => $options['top_k'],
                'top_p' => $options['top_p'],
                "num_thread" => env('OLLAMA_NUM_THREADS', 8)
            ],
            'stream' => $options['stream'],
        ]);

        if (!$response->successful()) {
            $errorMessage = 'API request failed: ' . $response->body();
            $this->logError('API request failed', $response->body());
            throw new Exception($this->formatErrorMessage($errorMessage));
        }

        $data = $response->json();
        return $data['response'] ?? '';
    }

    /**
     * Obtém a lista de modelos disponíveis no servidor Ollama.
     *
     * @return array Retorna um array associativo no formato ['model_id' => 'Model Name']
     */
    public function getAvailableModels(): array
    {
        if (!$this->isConfigured()) {
            $this->logError('API URL not configured');
            return [];
        }

        // Testar a conexão primeiro
        $this->testConnection();

        // Tentar vários endpoints possíveis
        $endpoint = "{$this->apiUrl}/tags";


        Log::info('Tentando obter modelos do endpoint', ['endpoint' => $endpoint]);

        $response = Http::timeout(5)->get($endpoint);

        if ($response->successful()) {
            $data = $response->json();
            Log::info('Resposta da API Ollama', ['data' => $data]);

            $models = [];

            // Processar a resposta para extrair os modelos
            if (isset($data['models']) && is_array($data['models'])) {
                foreach ($data['models'] as $model) {
                    if (isset($model['name'])) {
                        // Usar o nome como chave e valor
                        $name = $model['name'];
                        $displayName = $this->formatModelName($name);
                        $models[$name] = $displayName;
                    }
                }

                Log::info('Modelos encontrados', ['count' => count($models), 'models' => $models]);
                return $models;
            }
        }

        // Se chegou aqui, nenhum endpoint funcionou
        Log::error('Nenhum endpoint funcionou para obter modelos');

        // Retornar uma lista estática como fallback
        return [];
    }

    /**
     * Formata o nome do modelo para exibição.
     *
     * @param string $modelName
     * @return string
     */
    protected function formatModelName(string $modelName): string
    {
        // Remover a tag :latest se existir
        $name = str_replace(':latest', '', $modelName);

        // Capitalizar a primeira letra de cada palavra
        $name = ucwords(str_replace(['-', '_', ':'], ' ', $name));

        // Se tiver uma versão (ex: llama2:13b), formatar adequadamente
        if (strpos($modelName, ':') !== false) {
            $parts = explode(':', $modelName);
            $baseName = ucwords(str_replace(['-', '_'], ' ', $parts[0]));
            $version = $parts[1];

            // Se não for 'latest', adicionar a versão
            if ($version !== 'latest') {
                return "{$baseName} ({$version})";
            }

            return $baseName;
        }

        return $name;
    }
}
