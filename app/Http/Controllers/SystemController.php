<?php

namespace App\Http\Controllers;

use App\Services\AI\OllamaService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SystemController extends Controller
{
    /**
     * Verificar status do sistema
     */
    public function status(OllamaService $ollamaService): JsonResponse
    {
        $ollamaAvailable = $ollamaService->testConnection();

        return response()->json([
            'success' => true,
            'data' => [
                'system' => 'QueueAI',
                'version' => '1.0.0',
                'status' => 'running',
                'ollama' => [
                    'available' => $ollamaAvailable,
                    'url' => config('ai.ollama.api_url', env('OLLAMA_API_URL')),
                ],
                'queue' => [
                    'connection' => config('queue.default'),
                    'pending_jobs' => $this->getPendingJobsCount(),
                ],
                'timestamp' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Listar modelos disponíveis
     */
    public function models(OllamaService $ollamaService): JsonResponse
    {
        $models = $ollamaService->getAvailableModels();

        return response()->json([
            'success' => true,
            'data' => $models,
        ]);
    }

    /**
     * Estatísticas do sistema
     */
    public function stats(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'prompts' => [
                    'total' => \App\Models\Prompt::count(),
                    'pending' => \App\Models\Prompt::where('status', 'pending')->count(),
                    'processing' => \App\Models\Prompt::where('status', 'processing')->count(),
                    'completed' => \App\Models\Prompt::where('status', 'completed')->count(),
                    'failed' => \App\Models\Prompt::where('status', 'failed')->count(),
                ],
                'responses' => [
                    'total' => \App\Models\Response::count(),
                    'completed' => \App\Models\Response::where('status', 'completed')->count(),
                    'failed' => \App\Models\Response::where('status', 'failed')->count(),
                ],
                'queue' => [
                    'pending_jobs' => $this->getPendingJobsCount(),
                ],
            ],
        ]);
    }

    /**
     * Listar jobs na fila (todos os jobs - para admin)
     */
    public function queueJobs(): JsonResponse
    {
        $jobs = [];

        // Para SQLite/Database queue
        if (config('queue.default') === 'database') {
            $queueJobs = DB::table('jobs')
                ->orderBy('created_at', 'desc')
                ->get();

            foreach ($queueJobs as $job) {
                $payload = json_decode($job->payload, true);

                // Extrair informações do job ProcessPromptJob
                if (isset($payload['data']['commandName']) &&
                    $payload['data']['commandName'] === 'App\\Jobs\\ProcessPromptJob') {

                    $command = unserialize($payload['data']['command']);

                    $jobs[] = [
                        'id' => $job->id,
                        'prompt_uuid' => $command->prompt->uuid ?? null,
                        'model' => $command->model ?? null,
                        'created_at' => $job->created_at,
                        'attempts' => $job->attempts,
                        'queue' => $job->queue,
                        'user_id' => $command->prompt->user_id ?? null,
                    ];
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => $jobs,
        ]);
    }

    /**
     * Deletar job da fila
     */
    public function deleteQueueJob($jobId): JsonResponse
    {
        // Para SQLite/Database queue
        if (config('queue.default') === 'database') {
            $deleted = DB::table('jobs')->where('id', $jobId)->delete();

            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => 'Job removido da fila com sucesso.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Job não encontrado.',
                ], 404);
            }
        }

        return response()->json([
            'success' => false,
            'message' => 'Operação não suportada para este tipo de fila.',
        ], 400);
    }

    /**
     * Listar jobs na fila para o usuário autenticado
     */
    public function userQueueJobs(): JsonResponse
    {
        $userId = Auth::id();
        if (!$userId) {
            return response()->json([
                'success' => false,
                'message' => 'Usuário não autenticado',
            ], 401);
        }

        $jobs = [];

        // Para SQLite/Database queue
        if (config('queue.default') === 'database') {
            $queueJobs = DB::table('jobs')
                ->orderBy('created_at', 'desc')
                ->get();

            foreach ($queueJobs as $job) {
                $payload = json_decode($job->payload, true);

                // Extrair informações do job ProcessPromptJob
                if (isset($payload['data']['commandName']) &&
                    $payload['data']['commandName'] === 'App\\Jobs\\ProcessPromptJob') {

                    $command = unserialize($payload['data']['command']);

                    // Filtrar apenas jobs do usuário autenticado
                    if (isset($command->prompt->user_id) && $command->prompt->user_id == $userId) {
                        $jobs[] = [
                            'id' => $job->id,
                            'prompt_uuid' => $command->prompt->uuid ?? null,
                            'model' => $command->model ?? null,
                            'created_at' => $job->created_at,
                            'attempts' => $job->attempts,
                            'queue' => $job->queue,
                            'prompt_content' => substr($command->prompt->content ?? '', 0, 100) . '...',
                            'estimated_completion' => $this->estimateJobCompletion($job),
                        ];
                    }
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => $jobs,
        ]);
    }

    /**
     * Obter informações detalhadas do dashboard
     */
    public function dashboardData(OllamaService $ollamaService): JsonResponse
    {
        $userId = Auth::id();

        // Dados do Ollama
        $ollamaData = [
            'available' => $ollamaService->testConnection(),
            'url' => config('ai.ollama.api_url', env('OLLAMA_API_URL')),
            'models' => $ollamaService->getAvailableModels(),
            'running_models' => $this->getRunningModels($ollamaService),
            'version' => $this->getOllamaVersion($ollamaService),
        ];

        // Estatísticas do usuário
        $userStats = [];
        if ($userId) {
            $userStats = [
                'prompts' => [
                    'total' => \App\Models\Prompt::forUser($userId)->count(),
                    'pending' => \App\Models\Prompt::forUser($userId)->where('status', 'pending')->count(),
                    'processing' => \App\Models\Prompt::forUser($userId)->where('status', 'processing')->count(),
                    'completed' => \App\Models\Prompt::forUser($userId)->where('status', 'completed')->count(),
                    'failed' => \App\Models\Prompt::forUser($userId)->where('status', 'failed')->count(),
                ],
                'responses' => [
                    'total' => \App\Models\Response::whereHas('prompt', function($query) use ($userId) {
                        $query->where('user_id', $userId);
                    })->count(),
                    'completed' => \App\Models\Response::whereHas('prompt', function($query) use ($userId) {
                        $query->where('user_id', $userId);
                    })->where('status', 'completed')->count(),
                ],
                'queue_jobs' => $this->getUserQueueJobsCount($userId),
            ];
        }

        // Estatísticas do sistema (apenas para referência)
        $systemStats = [
            'queue' => [
                'connection' => config('queue.default'),
                'total_pending_jobs' => $this->getPendingJobsCount(),
            ],
            'timestamp' => now()->toISOString(),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'ollama' => $ollamaData,
                'user_stats' => $userStats,
                'system' => $systemStats,
            ],
        ]);
    }

    /**
     * Obter contagem de jobs pendentes
     */
    private function getPendingJobsCount(): int
    {
        // Para SQLite/Database queue
        if (config('queue.default') === 'database') {
            return DB::table('jobs')->count();
        }

        return 0;
    }

    /**
     * Obter contagem de jobs do usuário na fila
     */
    private function getUserQueueJobsCount(int $userId): int
    {
        $count = 0;

        if (config('queue.default') === 'database') {
            $queueJobs = DB::table('jobs')->get();

            foreach ($queueJobs as $job) {
                $payload = json_decode($job->payload, true);

                if (isset($payload['data']['commandName']) &&
                    $payload['data']['commandName'] === 'App\\Jobs\\ProcessPromptJob') {

                    $command = unserialize($payload['data']['command']);

                    if (isset($command->prompt->user_id) && $command->prompt->user_id == $userId) {
                        $count++;
                    }
                }
            }
        }

        return $count;
    }

    /**
     * Estimar tempo de conclusão do job
     */
    private function estimateJobCompletion($job): ?string
    {
        // Estimativa simples baseada na posição na fila e tempo médio de processamento
        $avgProcessingTime = 60; // 60 segundos por job (estimativa)
        $position = DB::table('jobs')->where('created_at', '<', $job->created_at)->count() + 1;

        $estimatedSeconds = $position * $avgProcessingTime;

        if ($estimatedSeconds < 60) {
            return "< 1 min";
        } elseif ($estimatedSeconds < 3600) {
            return round($estimatedSeconds / 60) . " min";
        } else {
            return round($estimatedSeconds / 3600, 1) . " h";
        }
    }

    /**
     * Obter modelos em execução no Ollama
     */
    private function getRunningModels(OllamaService $ollamaService): array
    {
        try {
            $response = Http::timeout(5)->get($ollamaService->getApiUrl() . '/ps');

            if ($response->successful()) {
                $data = $response->json();
                return $data['models'] ?? [];
            }
        } catch (\Exception $e) {
            Log::warning('Erro ao obter modelos em execução', ['error' => $e->getMessage()]);
        }

        return [];
    }

    /**
     * Obter versão do Ollama
     */
    private function getOllamaVersion(OllamaService $ollamaService): ?string
    {
        try {
            $response = Http::timeout(5)->get($ollamaService->getApiUrl() . '/version');

            if ($response->successful()) {
                $data = $response->json();
                return $data['version'] ?? null;
            }
        } catch (\Exception $e) {
            Log::warning('Erro ao obter versão do Ollama', ['error' => $e->getMessage()]);
        }

        return null;
    }

    /**
     * Obter estatísticas de uso dos modelos
     */
    public function modelUsageStats(): JsonResponse
    {
        $userId = Auth::id();

        // Obter estatísticas de uso por modelo para o usuário autenticado
        $modelStats = \App\Models\Response::whereHas('prompt', function($query) use ($userId) {
            $query->where('user_id', $userId);
        })
        ->selectRaw('model, COUNT(*) as usage_count, AVG(CASE WHEN status = "completed" THEN 1 ELSE 0 END) * 100 as success_rate')
        ->groupBy('model')
        ->orderBy('usage_count', 'desc')
        ->get()
        ->map(function($stat) {
            return [
                'model' => $stat->model,
                'usage_count' => $stat->usage_count,
                'success_rate' => round($stat->success_rate, 1)
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $modelStats,
        ]);
    }

    /**
     * Obter histórico da fila nas últimas 24 horas
     */
    public function queueHistoryStats(): JsonResponse
    {
        $userId = Auth::id();

        // Gerar dados históricos baseados nos jobs criados nas últimas 24 horas
        $hours = [];
        $queueData = [];
        $now = now();

        for ($i = 23; $i >= 0; $i--) {
            $hourStart = $now->copy()->subHours($i)->startOfHour();
            $hourEnd = $hourStart->copy()->endOfHour();

            $hours[] = $hourStart->format('H:00');

            // Contar jobs criados nesta hora para o usuário
            $jobsCount = \App\Models\Response::whereHas('prompt', function($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->whereBetween('created_at', [$hourStart, $hourEnd])
            ->count();

            $queueData[] = $jobsCount;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $hours,
                'data' => $queueData,
            ],
        ]);
    }
}
