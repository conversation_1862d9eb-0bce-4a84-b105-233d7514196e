<?php

namespace App\Http\Controllers;

use App\Models\Subject;
use App\Http\Requests\StoreSubjectRequest;
use App\Http\Requests\UpdateSubjectRequest;
use App\Http\Resources\SubjectResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class SubjectController extends Controller
{
    /**
     * Display a listing of subjects for the authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        $query = Subject::with(['user', 'parent', 'children'])
            ->forUser(Auth::id());

        // Filtrar apenas assuntos raiz se solicitado
        if ($request->boolean('roots_only')) {
            $query->roots();
        }

        // Filtrar por assunto pai se especificado
        if ($request->has('parent_id')) {
            $query->where('parent_id', $request->parent_id);
        }

        $subjects = $query->orderBy('name')->get();

        return response()->json([
            'data' => SubjectResource::collection($subjects),
            'message' => 'Assuntos recuperados com sucesso'
        ]);
    }

    /**
     * Store a newly created subject
     */
    public function store(StoreSubjectRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $subject = Subject::create([
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'user_id' => Auth::id(),
            'parent_id' => $validated['parent_id'] ?? null
        ]);

        $subject->load(['user', 'parent', 'children']);

        return response()->json([
            'data' => new SubjectResource($subject),
            'message' => 'Assunto criado com sucesso'
        ], 201);
    }

    /**
     * Display the specified subject
     */
    public function show(Subject $subject): JsonResponse
    {
        // Verificar se o assunto pertence ao usuário autenticado
        if ($subject->user_id !== Auth::id()) {
            return response()->json([
                'message' => 'Assunto não encontrado'
            ], 404);
        }

        $subject->load(['user', 'parent', 'children', 'prompts']);

        return response()->json([
            'data' => new SubjectResource($subject),
            'message' => 'Assunto recuperado com sucesso'
        ]);
    }

    /**
     * Update the specified subject
     */
    public function update(UpdateSubjectRequest $request, Subject $subject): JsonResponse
    {
        $validated = $request->validated();

        $subject->update($validated);
        $subject->load(['user', 'parent', 'children']);

        return response()->json([
            'data' => new SubjectResource($subject),
            'message' => 'Assunto atualizado com sucesso'
        ]);
    }

    /**
     * Remove the specified subject
     */
    public function destroy(Subject $subject): JsonResponse
    {
        // Verificar se o assunto pertence ao usuário autenticado
        if ($subject->user_id !== Auth::id()) {
            return response()->json([
                'message' => 'Assunto não encontrado'
            ], 404);
        }

        // Verificar se há assuntos filhos
        if ($subject->children()->count() > 0) {
            return response()->json([
                'message' => 'Não é possível excluir um assunto que possui sub-assuntos'
            ], 422);
        }

        $subject->delete();

        return response()->json([
            'message' => 'Assunto excluído com sucesso'
        ]);
    }

    /**
     * Get all root subjects (subjects without parent) for the authenticated user
     */
    public function roots(): JsonResponse
    {
        $subjects = Subject::with(['user', 'children'])
            ->forUser(Auth::id())
            ->roots()
            ->orderBy('name')
            ->get();

        return response()->json([
            'data' => SubjectResource::collection($subjects),
            'message' => 'Assuntos raiz recuperados com sucesso'
        ]);
    }

    /**
     * Get all children of a specific subject
     */
    public function children(Subject $subject): JsonResponse
    {
        // Verificar se o assunto pertence ao usuário autenticado
        if ($subject->user_id !== Auth::id()) {
            return response()->json([
                'message' => 'Assunto não encontrado'
            ], 404);
        }

        $children = $subject->children()
            ->with(['user', 'children'])
            ->orderBy('name')
            ->get();

        return response()->json([
            'data' => SubjectResource::collection($children),
            'message' => 'Sub-assuntos recuperados com sucesso'
        ]);
    }
}
