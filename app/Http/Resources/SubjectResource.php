<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'name' => $this->name,
            'description' => $this->description,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Relações
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                ];
            }),

            'parent' => $this->whenLoaded('parent', function () {
                return $this->parent ? new SubjectResource($this->parent) : null;
            }),

            'children' => $this->whenLoaded('children', function () {
                return SubjectResource::collection($this->children);
            }),

            'prompts' => $this->whenLoaded('prompts', function () {
                return $this->prompts->map(function ($prompt) {
                    return [
                        'id' => $prompt->id,
                        'uuid' => $prompt->uuid,
                        'content' => $prompt->content,
                        'status' => $prompt->status,
                        'created_at' => $prompt->created_at?->toISOString(),
                    ];
                });
            }),

            // Contadores úteis
            'children_count' => $this->whenCounted('children'),
            'prompts_count' => $this->whenCounted('prompts'),

            // Informações adicionais
            'is_root' => is_null($this->parent_id),
            'has_children' => $this->children()->exists(),
        ];
    }
}
