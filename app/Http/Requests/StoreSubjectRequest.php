<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\Subject;

class StoreSubjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'parent_id' => [
                'nullable',
                'exists:subjects,id',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        $parent = Subject::where('id', $value)
                            ->where('user_id', Auth::id())
                            ->first();

                        if (!$parent) {
                            $fail('O assunto pai selecionado não existe ou não pertence ao usuário.');
                        }
                    }
                }
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'O nome do assunto é obrigatório.',
            'name.max' => 'O nome do assunto não pode ter mais de 255 caracteres.',
            'description.max' => 'A descrição não pode ter mais de 1000 caracteres.',
            'parent_id.exists' => 'O assunto pai selecionado não existe.'
        ];
    }
}
