<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\Subject;

class UpdateSubjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Verificar se o usuário está autenticado e se o assunto pertence a ele
        $subject = $this->route('subject');
        return Auth::check() && $subject && $subject->user_id === Auth::id();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $subject = $this->route('subject');

        return [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'parent_id' => [
                'nullable',
                'exists:subjects,id',
                function ($attribute, $value, $fail) use ($subject) {
                    if ($value) {
                        // Verificar se não está tentando ser pai de si mesmo
                        if ($value == $subject->id) {
                            $fail('Um assunto não pode ser pai de si mesmo.');
                            return;
                        }

                        // Verificar se o pai pertence ao usuário
                        $parent = Subject::where('id', $value)
                            ->where('user_id', Auth::id())
                            ->first();

                        if (!$parent) {
                            $fail('O assunto pai selecionado não existe ou não pertence ao usuário.');
                        }
                    }
                }
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'O nome do assunto é obrigatório.',
            'name.max' => 'O nome do assunto não pode ter mais de 255 caracteres.',
            'description.max' => 'A descrição não pode ter mais de 1000 caracteres.',
            'parent_id.exists' => 'O assunto pai selecionado não existe.'
        ];
    }
}
